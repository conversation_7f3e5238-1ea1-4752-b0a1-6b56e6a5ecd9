using UnityEngine;

public enum BrushMode
{
    Grid,
    Single,
    Physics,
    Spline,
    Square,
    Rectangle,
    Circle
}

[System.Serializable]
public class Prefabpaintertool : MonoBehaviour
{
    [Header("Brush Mode")]
    public BrushMode brushMode = BrushMode.Grid;

    [Header("Prefab Settings")]
    public GameObject[] prefabsToPlace;
    public int selectedPrefabIndex = 0;
    public bool placeMultiplePrefabs = false;
    public bool randomPrefabSelection = false;

    [Header("Rotation Settings")]
    public bool enableRotation = false;
    public Vector3 baseRotation = Vector3.zero;
    public bool randomRotation = false;
    public Vector3 randomRotationRange = new(0, 360, 0);
    public float rotationStep = 45f;

    [Header("Terrain Slope Rotation")]
    public bool alignToTerrainSlope = true;
    [Range(0f, 1f)]
    public float slopeInfluence = 1f;

    [Header("Ground Placement")]
    public bool keepObjectsOnGround = true;

    [Header("Single Brush Settings")]
    public bool enableBrushPreview = true;
    public Color brushPreviewColor = Color.cyan;
    public float brushPreviewSize = 1f;
    [Range(0.1f, 5f)]
    public float minPlacementDistance = 0.5f;
    public bool snapSingleToTerrain = true;

    [Header("Physics Brush Settings")]
    public bool requireCollider = true;
    public bool requireRigidbody = true;
    public Color physicsPreviewColor = Color.red;
    [Range(0.5f, 20f)]
    public float dropHeight = 5f;
    public bool enablePhysicsSimulation = true;
    public bool autoStopPhysics = false;
    [Range(0.1f, 10f)]
    public float simulationTime = 2f;
    public bool showDropPreview = true;
    public bool snapPhysicsToTerrain = true;

    [Header("Advanced Physics Settings")]
    [Range(0f, 10f)]
    public float forceIntensity = 2f;
    [Range(0f, 10f)]
    public float torqueIntensity = 5f;
    public bool addRandomBounce = true;
    [Range(0f, 1f)]
    public float bounciness = 0.6f;
    public bool varyMass = true;
    [Range(0.1f, 5f)]
    public float minMass = 0.5f;
    [Range(0.1f, 5f)]
    public float maxMass = 2f;

    [Header("Environmental Effects")]
    public bool addWindEffect = true;
    [Range(0f, 5f)]
    public float windStrength = 1f;
    public Vector3 windDirection = new Vector3(1, 0, 1);
    public bool addTumbleEffect = true;
    [Range(0f, 2f)]
    public float tumbleChance = 0.3f;

    [Header("Spline Brush Settings")]
    public Color splineColor = Color.blue;
    [Range(0.1f, 5f)]
    public float splineSpacing = 1f;
    public bool autoRotateToPath = true;
    public bool smoothSpline = true;
    [Range(0.1f, 2f)]
    public float splineWidth = 0.5f;
    public bool showSplinePreview = true;
    public bool snapToGround = true;
    [Range(2, 20)]
    public int splineResolution = 10;
    public bool closedSpline = false;

    [Header("Shape Brush Settings")]
    public Color shapePreviewColor = Color.cyan;
    public bool fillShape = false;
    public bool outlineOnly = true;
    [Range(0.1f, 3f)]
    public float shapeSpacing = 1f;
    [Range(1f, 20f)]
    public float shapeSize = 5f;
    public bool showShapePreview = true;
    public bool snapShapeToGround = true;

    [Header("Rectangle Settings")]
    [Range(1f, 20f)]
    public float rectangleWidth = 5f;
    [Range(1f, 20f)]
    public float rectangleHeight = 3f;

    [Header("Circle Settings")]
    [Range(1f, 20f)]
    public float circleRadius = 3f;
    [Range(3, 32)]
    public int circleSegments = 16;

    [Header("Grid Settings")]
    public float gridSize = 1f;
    public float rowSpacing = 1f;
    public float columnSpacing = 1f;
    public bool useCustomDimensions = false;
    public int customRows = 5;
    public int customColumns = 5;
    public LayerMask groundLayer = 1;
    public bool snapGridToTerrain = true;

    [Header("Visual Settings")]
    public bool showGridPreview = true;
    public Color gridColor = Color.green;
    public bool enableGridResize = false;
    public Color resizeHandleColor = Color.yellow;

    // Simple data holder for the editor tool
    // The actual functionality is in the Editor script
}
